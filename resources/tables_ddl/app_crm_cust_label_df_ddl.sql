-- 名词解释：
-- 代售：特指‘全品类’商品，也就是sub_type=1或2的商品；
-- 自营品牌：PB品，比如Protag、C味、善橙等自有品牌；
-- 类目大分类：乳制品、鲜果、其他；


CREATE TABLE IF NOT EXISTS app_crm_cust_label_df(
  cust_id BIGINT COMMENT '客户编号',
   cust_name STRING COMMENT '客户名',
   operate_status BIGINT COMMENT '运营状态:正常(0),倒闭(1)',
   register_time DATETIME COMMENT '注册时间，例如：2016-09-06 18:55:39',
   audit_time DATETIME COMMENT '审核通过时间，例如：2018-03-05 10:46:20',
   abandon_date BIGINT COMMENT '废弃日期，yyyyMMdd，指是否被拉黑或者已经成为公海客户。如果=99991231，则表示未废弃。',
   islock BIGINT COMMENT '审核状态：0、审核通过 1、审核中 2、审核未通过 3、账号被拉黑',
   is_audit_30d BIGINT COMMENT '是否在近30日审核通过：0、否 1、是',
   is_first_order_30d BIGINT COMMENT '是否在近30日是首单客户：0、否 1、是',
   is_first_register_30d BIGINT COMMENT '是否在近30日是首次注册客户：0、否 1、是',
   cust_type STRING COMMENT '客户类型；枚举：面包蛋糕,西餐,咖啡,甜品冰淇淋,茶饮,其他',
   cust_team STRING COMMENT '客户团队类型:集团大客户（茶百道）、集团大客户（喜茶）、Mars大客户、平台客户',
   brand_id BIGINT COMMENT '品牌ID，即大客户ID',
   brand_name STRING COMMENT '品牌公司全称（工商局注册的名字）',
   brand_alias STRING COMMENT '品牌别名，比如霸王茶姬、星巴克等等',
   city_id BIGINT COMMENT '运营服务区编码',
   city_name STRING COMMENT '运营服务区名称，比如‘茶外区’，‘杭州’等',
   register_province STRING COMMENT '门店注册地址：省',
   register_city STRING COMMENT '门店注册地址：市',
   register_area STRING COMMENT '门店注册地址：区',
   bd_id BIGINT COMMENT '门店当前归属的销售ID',
   bd_name STRING COMMENT '门店当前归属的销售名称',
   large_area_id BIGINT COMMENT '运营服务大区id',
   large_area_name STRING COMMENT '运营服务大区名字',
   bd_zone STRING COMMENT '销售人员归属的销售团队名字，通常是BD的上级M1的团队名字',
   m1 STRING COMMENT 'BD的上级，正式职务是销售主管，城市负责人',
   m2 STRING COMMENT 'M1的上级，正式职务是销售经理，区域负责人',
   m3 STRING COMMENT 'M2的上级，正式职务是销售总监，部门负责人',
   is_disabled_bd BIGINT COMMENT 'bd是否离职',
   frequency DECIMAL(38,18) COMMENT '门店近1年平均下单周期（超过60天取60天）',
   standard_line STRING COMMENT '近1年平均下单周期标准线标记，枚举:over，表示超过平均值, normal，表示平均水平, no_deal 表示最近1年无下单记录, first_deal 表示首次下单',
   first_order_days BIGINT COMMENT '首次下单距今天数',
   last_order_days BIGINT COMMENT '最近一次下单距今天数',
   first_delivery_days BIGINT COMMENT '首次配送距今天数',
   last_delivery_days BIGINT COMMENT '最近一次配送距今天数',
   life_cycle STRING COMMENT '生命周期标签（粗），枚举值：[导入期、成长期、已流失期、稳定期、适应期]',
   first_order_time DATETIME COMMENT '首次下单时间，例如 2016-09-24 13:54:07',
   last_order_time DATETIME COMMENT '最近一次下单时间，例如 2017-04-05 20:19:46',
   order_real_amt_7d DECIMAL(38,18) COMMENT '最近7天下单实付金额',
   order_real_amt_14d DECIMAL(38,18) COMMENT '最近14天下单实付金额',
   order_real_amt_30d DECIMAL(38,18) COMMENT '最近30天下单实付金额',
   order_real_amt_60d DECIMAL(38,18) COMMENT '最近60天下单实付金额',
   order_real_amt_180d DECIMAL(38,18) COMMENT '最近180天下单实付金额',
   order_real_amt_365d DECIMAL(38,18) COMMENT '最近365天下单实付金额',
   order_real_amt_his DECIMAL(38,18) COMMENT '历史所有下单实付金额',
   order_origin_amt_7d DECIMAL(38,18) COMMENT '最近7天下单应付金额',
   order_origin_amt_14d DECIMAL(38,18) COMMENT '最近14天下单应付金额',
   order_origin_amt_30d DECIMAL(38,18) COMMENT '最近30天下单应付金额',
   order_origin_amt_60d DECIMAL(38,18) COMMENT '最近60天下单应付金额',
   order_origin_amt_180d DECIMAL(38,18) COMMENT '最近180天下单应付金额',
   order_origin_amt_365d DECIMAL(38,18) COMMENT '最近365天下单应付金额',
   order_origin_amt_his DECIMAL(38,18) COMMENT '历史所有下单应付金额',
   order_days_7d BIGINT COMMENT '最近7天下单频次（下单天数）',
   order_days_14d BIGINT COMMENT '最近14天下单频次（下单天数）',
   order_days_30d BIGINT COMMENT '最近30天下单频次（下单天数）',
   order_days_60d BIGINT COMMENT '最近60天下单频次（下单天数）',
   order_days_180d BIGINT COMMENT '最近180天下单频次（下单天数）',
   order_days_365d BIGINT COMMENT '最近365天下单频次（下单天数）',
   order_days_his BIGINT COMMENT '历史所有下单频次（下单天数）',
   order_cnt_7d BIGINT COMMENT '最近7天订单数',
   order_cnt_14d BIGINT COMMENT '最近14天订单数',
   order_cnt_30d BIGINT COMMENT '最近30天订单数',
   order_cnt_60d BIGINT COMMENT '最近60天订单数',
   order_cnt_180d BIGINT COMMENT '最近180天订单数',
   order_cnt_365d BIGINT COMMENT '最近365天订单数',
   order_cnt_his BIGINT COMMENT '历史所有订单数',
   order_fruit_real_amt_7d DECIMAL(38,18) COMMENT '最近7天鲜果下单实付金额',
   order_fruit_real_amt_14d DECIMAL(38,18) COMMENT '最近14天鲜果下单实付金额',
   order_fruit_real_amt_30d DECIMAL(38,18) COMMENT '最近30天鲜果下单实付金额',
   order_fruit_real_amt_60d DECIMAL(38,18) COMMENT '最近60天鲜果下单实付金额',
   order_fruit_real_amt_180d DECIMAL(38,18) COMMENT '最近180天鲜果下单实付金额',
   order_fruit_real_amt_365d DECIMAL(38,18) COMMENT '最近365天鲜果下单实付金额',
   order_fruit_real_amt_his DECIMAL(38,18) COMMENT '历史所有鲜果下单实付金额',
   order_fruit_origin_amt_7d DECIMAL(38,18) COMMENT '最近7天鲜果下单应付金额',
   order_fruit_origin_amt_14d DECIMAL(38,18) COMMENT '最近14天鲜果下单应付金额',
   order_fruit_origin_amt_30d DECIMAL(38,18) COMMENT '最近30天鲜果下单应付金额',
   order_fruit_origin_amt_60d DECIMAL(38,18) COMMENT '最近60天鲜果下单应付金额',
   order_fruit_origin_amt_180d DECIMAL(38,18) COMMENT '最近180天鲜果下单应付金额',
   order_fruit_origin_amt_365d DECIMAL(38,18) COMMENT '最近365天鲜果下单应付金额',
   order_fruit_origin_amt_his DECIMAL(38,18) COMMENT '历史所有鲜果下单应付金额',
   order_dairy_real_amt_7d DECIMAL(38,18) COMMENT '最近7天乳制品下单实付金额',
   order_dairy_real_amt_14d DECIMAL(38,18) COMMENT '最近14天乳制品下单实付金额',
   order_dairy_real_amt_30d DECIMAL(38,18) COMMENT '最近30天乳制品下单实付金额',
   order_dairy_real_amt_60d DECIMAL(38,18) COMMENT '最近60天乳制品下单实付金额',
   order_dairy_real_amt_180d DECIMAL(38,18) COMMENT '最近180天乳制品下单实付金额',
   order_dairy_real_amt_365d DECIMAL(38,18) COMMENT '最近365天乳制品下单实付金额',
   order_dairy_real_amt_his DECIMAL(38,18) COMMENT '历史所有乳制品下单实付金额',
   order_dairy_origin_amt_7d DECIMAL(38,18) COMMENT '最近7天乳制品下单应付金额',
   order_dairy_origin_amt_14d DECIMAL(38,18) COMMENT '最近14天乳制品下单应付金额',
   order_dairy_origin_amt_30d DECIMAL(38,18) COMMENT '最近30天乳制品下单应付金额',
   order_dairy_origin_amt_60d DECIMAL(38,18) COMMENT '最近60天乳制品下单应付金额',
   order_dairy_origin_amt_180d DECIMAL(38,18) COMMENT '最近180天乳制品下单应付金额',
   order_dairy_origin_amt_365d DECIMAL(38,18) COMMENT '最近365天乳制品下单应付金额',
   order_dairy_origin_amt_his DECIMAL(38,18) COMMENT '历史所有乳制品下单应付金额',
   order_other_real_amt_7d DECIMAL(38,18) COMMENT '最近7天其他(非鲜果、非乳制品,下同)的下单实付金额',
   order_other_real_amt_14d DECIMAL(38,18) COMMENT '最近14天其他的下单实付金额',
   order_other_real_amt_30d DECIMAL(38,18) COMMENT '最近30天其他的下单实付金额',
   order_other_real_amt_60d DECIMAL(38,18) COMMENT '最近60天其他的下单实付金额',
   order_other_real_amt_180d DECIMAL(38,18) COMMENT '最近180天其他的下单实付金额',
   order_other_real_amt_365d DECIMAL(38,18) COMMENT '最近365天其他的下单实付金额',
   order_other_real_amt_his DECIMAL(38,18) COMMENT '历史所有其他的下单实付金额',
   order_other_origin_amt_7d DECIMAL(38,18) COMMENT '最近7天其他的下单应付金额',
   order_other_origin_amt_14d DECIMAL(38,18) COMMENT '最近14天其他的下单应付金额',
   order_other_origin_amt_30d DECIMAL(38,18) COMMENT '最近30天其他的下单应付金额',
   order_other_origin_amt_60d DECIMAL(38,18) COMMENT '最近60天其他的下单应付金额',
   order_other_origin_amt_180d DECIMAL(38,18) COMMENT '最近180天其他的下单应付金额',
   order_other_origin_amt_365d DECIMAL(38,18) COMMENT '最近365天其他的下单应付金额',
   order_other_origin_amt_his DECIMAL(38,18) COMMENT '历史所有其他的下单应付金额',
   order_self_real_amt_7d DECIMAL(38,18) COMMENT '最近7天自营品牌(PB品，比如Protag、C味、善橙等自有品牌，下同)下单实付金额',
   order_self_real_amt_14d DECIMAL(38,18) COMMENT '最近14天自营品牌下单实付金额',
   order_self_real_amt_30d DECIMAL(38,18) COMMENT '最近30天自营品牌下单实付金额',
   order_self_real_amt_60d DECIMAL(38,18) COMMENT '最近60天自营品牌下单实付金额',
   order_self_real_amt_180d DECIMAL(38,18) COMMENT '最近180天自营品牌下单实付金额',
   order_self_real_amt_365d DECIMAL(38,18) COMMENT '最近365天自营品牌下单实付金额',
   order_self_real_amt_his DECIMAL(38,18) COMMENT '历史所有自营品牌下单实付金额',
   order_self_origin_amt_7d DECIMAL(38,18) COMMENT '最近7天自营品牌下单应付金额',
   order_self_origin_amt_14d DECIMAL(38,18) COMMENT '最近14天自营品牌下单应付金额',
   order_self_origin_amt_30d DECIMAL(38,18) COMMENT '最近30天自营品牌下单应付金额',
   order_self_origin_amt_60d DECIMAL(38,18) COMMENT '最近60天自营品牌下单应付金额',
   order_self_origin_amt_180d DECIMAL(38,18) COMMENT '最近180天自营品牌下单应付金额',
   order_self_origin_amt_365d DECIMAL(38,18) COMMENT '最近365天自营品牌下单应付金额',
   order_self_origin_amt_his DECIMAL(38,18) COMMENT '历史所有自营品牌下单应付金额',
   order_timing_real_amt_his DECIMAL(38,18) COMMENT '历史所有省心送下单实付金额',
   order_timing_origin_amt_his DECIMAL(38,18) COMMENT '历史所有省心送下单应付金额',
   order_real_amt_30d_last DECIMAL(38,18) COMMENT '上个周期（距30-60天）下单实付金额',
   order_origin_amt_30d_last DECIMAL(38,18) COMMENT '上个周期（距30-60天）下单应付金额',
   order_days_30d_last BIGINT COMMENT '上个周期（距30-60天）下单频次（下单天数）',
   coupon_amt_7d DECIMAL(38,18) COMMENT '最近7天优惠券金额（不包括运费券和售后券）',
   coupon_amt_14d DECIMAL(38,18) COMMENT '最近14天优惠券金额（不包括运费券和售后券）',
   coupon_amt_30d DECIMAL(38,18) COMMENT '最近30天优惠券金额（不包括运费券和售后券）',
   coupon_amt_60d DECIMAL(38,18) COMMENT '最近60天优惠券金额（不包括运费券和售后券）',
   coupon_amt_180d DECIMAL(38,18) COMMENT '最近180天优惠券金额（不包括运费券和售后券）',
   coupon_amt_365d DECIMAL(38,18) COMMENT '最近365天优惠券金额（不包括运费券和售后券）',
   coupon_amt_his DECIMAL(38,18) COMMENT '历史所有优惠券金额（不包括运费券和售后券）',
   deliver_coupon_amt_7d DECIMAL(38,18) COMMENT '最近7天运费券金额',
   deliver_coupon_amt_14d DECIMAL(38,18) COMMENT '最近14天运费券金额',
   deliver_coupon_amt_30d DECIMAL(38,18) COMMENT '最近30天运费券金额',
   deliver_coupon_amt_60d DECIMAL(38,18) COMMENT '最近60天运费券金额',
   deliver_coupon_amt_180d DECIMAL(38,18) COMMENT '最近180天运费券金额',
   deliver_coupon_amt_365d DECIMAL(38,18) COMMENT '最近365天运费券金额',
   deliver_coupon_amt_his DECIMAL(38,18) COMMENT '历史所有运费券金额',
   after_sale_coupon_amt_7d DECIMAL(38,18) COMMENT '最近7天售后券金额',
   after_sale_coupon_amt_14d DECIMAL(38,18) COMMENT '最近14天售后券金额',
   after_sale_coupon_amt_30d DECIMAL(38,18) COMMENT '最近30天售后券金额',
   after_sale_coupon_amt_60d DECIMAL(38,18) COMMENT '最近60天售后券金额',
   after_sale_coupon_amt_180d DECIMAL(38,18) COMMENT '最近180天售后券金额',
   after_sale_coupon_amt_365d DECIMAL(38,18) COMMENT '最近365天售后券金额',
   after_sale_coupon_amt_his DECIMAL(38,18) COMMENT '历史所有售后券金额',
   after_sale_amt_7d DECIMAL(38,18) COMMENT '最近7天售后金额',
   after_sale_amt_14d DECIMAL(38,18) COMMENT '最近14天售后金额',
   after_sale_amt_30d DECIMAL(38,18) COMMENT '最近30天售后金额',
   after_sale_amt_60d DECIMAL(38,18) COMMENT '最近60天售后金额',
   after_sale_amt_180d DECIMAL(38,18) COMMENT '最近180天售后金额',
   after_sale_amt_365d DECIMAL(38,18) COMMENT '最近365天售后金额',
   after_sale_amt_his DECIMAL(38,18) COMMENT '历史所有售后金额',
   fruit_after_sale_amt_7d DECIMAL(38,18) COMMENT '最近7天鲜果售后金额',
   fruit_after_sale_amt_14d DECIMAL(38,18) COMMENT '最近14天鲜果售后金额',
   fruit_after_sale_amt_30d DECIMAL(38,18) COMMENT '最近30天鲜果售后金额',
   fruit_after_sale_amt_60d DECIMAL(38,18) COMMENT '最近60天鲜果售后金额',
   fruit_after_sale_amt_180d DECIMAL(38,18) COMMENT '最近180天鲜果售后金额',
   fruit_after_sale_amt_365d DECIMAL(38,18) COMMENT '最近365天鲜果售后金额',
   fruit_after_sale_amt_his DECIMAL(38,18) COMMENT '历史所有鲜果售后金额',
   dairy_after_sale_amt_7d DECIMAL(38,18) COMMENT '最近7天乳制品售后金额',
   dairy_after_sale_amt_14d DECIMAL(38,18) COMMENT '最近14天乳制品售后金额',
   dairy_after_sale_amt_30d DECIMAL(38,18) COMMENT '最近30天乳制品售后金额',
   dairy_after_sale_amt_60d DECIMAL(38,18) COMMENT '最近60天乳制品售后金额',
   dairy_after_sale_amt_180d DECIMAL(38,18) COMMENT '最近180天乳制品售后金额',
   dairy_after_sale_amt_365d DECIMAL(38,18) COMMENT '最近365天乳制品售后金额',
   dairy_after_sale_amt_his DECIMAL(38,18) COMMENT '历史所有乳制品售后金额',
   other_after_sale_amt_7d DECIMAL(38,18) COMMENT '最近7天其他售后金额',
   other_after_sale_amt_14d DECIMAL(38,18) COMMENT '最近14天其他售后金额',
   other_after_sale_amt_30d DECIMAL(38,18) COMMENT '最近30天其他售后金额',
   other_after_sale_amt_60d DECIMAL(38,18) COMMENT '最近60天其他售后金额',
   other_after_sale_amt_180d DECIMAL(38,18) COMMENT '最近180天其他售后金额',
   other_after_sale_amt_365d DECIMAL(38,18) COMMENT '最近365天其他售后金额',
   other_after_sale_amt_his DECIMAL(38,18) COMMENT '历史所有其他售后金额',
   self_after_sale_amt_7d DECIMAL(38,18) COMMENT '最近7天自营品牌售后金额',
   self_after_sale_amt_14d DECIMAL(38,18) COMMENT '最近14天自营品牌售后金额',
   self_after_sale_amt_30d DECIMAL(38,18) COMMENT '最近30天自营品牌售后金额',
   self_after_sale_amt_60d DECIMAL(38,18) COMMENT '最近60天自营品牌售后金额',
   self_after_sale_amt_180d DECIMAL(38,18) COMMENT '最近180天自营品牌售后金额',
   self_after_sale_amt_365d DECIMAL(38,18) COMMENT '最近365天自营品牌售后金额',
   self_after_sale_amt_his DECIMAL(38,18) COMMENT '历史所有自营品牌售后金额',
   timing_after_sale_amt_his DECIMAL(38,18) COMMENT '历史所有省心送售后金额',
   first_delivery_time DATETIME COMMENT '最早一次配送时间',
   last_delivery_time DATETIME COMMENT '最近一次配送时间',
   delivery_real_amt_7d DECIMAL(38,18) COMMENT '最近7天履约实付金额',
   delivery_real_amt_14d DECIMAL(38,18) COMMENT '最近14天履约实付金额',
   delivery_real_amt_30d DECIMAL(38,18) COMMENT '最近30天履约实付金额',
   delivery_real_amt_60d DECIMAL(38,18) COMMENT '最近60天履约实付金额',
   delivery_real_amt_180d DECIMAL(38,18) COMMENT '最近180天履约实付金额',
   delivery_real_amt_365d DECIMAL(38,18) COMMENT '最近365天履约实付金额',
   delivery_real_amt_his DECIMAL(38,18) COMMENT '历史所有履约实付金额',
   delivery_cost_amt_7d DECIMAL(38,18) COMMENT '最近7天履约成本金额',
   delivery_cost_amt_14d DECIMAL(38,18) COMMENT '最近14天履约成本金额',
   delivery_cost_amt_30d DECIMAL(38,18) COMMENT '最近30天履约成本金额',
   delivery_cost_amt_60d DECIMAL(38,18) COMMENT '最近60天履约成本金额',
   delivery_cost_amt_180d DECIMAL(38,18) COMMENT '最近180天履约成本金额',
   delivery_cost_amt_365d DECIMAL(38,18) COMMENT '最近365天履约成本金额',
   delivery_cost_amt_his DECIMAL(38,18) COMMENT '历史所有履约成本金额',
   delivery_point_cnt_7d BIGINT COMMENT '最近7天配送点位数',
   delivery_point_cnt_14d BIGINT COMMENT '最近14天配送点位数',
   delivery_point_cnt_30d BIGINT COMMENT '最近30天配送点位数',
   delivery_point_cnt_60d BIGINT COMMENT '最近60天配送点位数',
   delivery_point_cnt_180d BIGINT COMMENT '最近180天配送点位数',
   delivery_point_cnt_365d BIGINT COMMENT '最近365天配送点位数',
   delivery_point_cnt_his BIGINT COMMENT '历史所有配送点位数',
   delivery_fruit_real_amt_7d DECIMAL(38,18) COMMENT '最近7天鲜果履约实付金额',
   delivery_fruit_real_amt_14d DECIMAL(38,18) COMMENT '最近14天鲜果履约实付金额',
   delivery_fruit_real_amt_30d DECIMAL(38,18) COMMENT '最近30天鲜果履约实付金额',
   delivery_fruit_real_amt_60d DECIMAL(38,18) COMMENT '最近60天鲜果履约实付金额',
   delivery_fruit_real_amt_180d DECIMAL(38,18) COMMENT '最近180天鲜果履约实付金额',
   delivery_fruit_real_amt_365d DECIMAL(38,18) COMMENT '最近365天鲜果履约实付金额',
   delivery_fruit_real_amt_his DECIMAL(38,18) COMMENT '历史所有鲜果履约实付金额',
   delivery_fruit_cost_amt_7d DECIMAL(38,18) COMMENT '最近7天鲜果履约成本金额',
   delivery_fruit_cost_amt_14d DECIMAL(38,18) COMMENT '最近14天鲜果履约成本金额',
   delivery_fruit_cost_amt_30d DECIMAL(38,18) COMMENT '最近30天鲜果履约成本金额',
   delivery_fruit_cost_amt_60d DECIMAL(38,18) COMMENT '最近60天鲜果履约成本金额',
   delivery_fruit_cost_amt_180d DECIMAL(38,18) COMMENT '最近180天鲜果履约成本金额',
   delivery_fruit_cost_amt_365d DECIMAL(38,18) COMMENT '最近365天鲜果履约成本金额',
   delivery_fruit_cost_amt_his DECIMAL(38,18) COMMENT '历史所有鲜果履约成本金额',
   delivery_dairy_real_amt_7d DECIMAL(38,18) COMMENT '最近7天乳制品履约实付金额',
   delivery_dairy_real_amt_14d DECIMAL(38,18) COMMENT '最近14天乳制品履约实付金额',
   delivery_dairy_real_amt_30d DECIMAL(38,18) COMMENT '最近30天乳制品履约实付金额',
   delivery_dairy_real_amt_60d DECIMAL(38,18) COMMENT '最近60天乳制品履约实付金额',
   delivery_dairy_real_amt_180d DECIMAL(38,18) COMMENT '最近180天乳制品履约实付金额',
   delivery_dairy_real_amt_365d DECIMAL(38,18) COMMENT '最近365天乳制品履约实付金额',
   delivery_dairy_real_amt_his DECIMAL(38,18) COMMENT '历史所有乳制品履约实付金额',
   delivery_dairy_cost_amt_7d DECIMAL(38,18) COMMENT '最近7天乳制品履约成本金额',
   delivery_dairy_cost_amt_14d DECIMAL(38,18) COMMENT '最近14天乳制品履约成本金额',
   delivery_dairy_cost_amt_30d DECIMAL(38,18) COMMENT '最近30天乳制品履约成本金额',
   delivery_dairy_cost_amt_60d DECIMAL(38,18) COMMENT '最近60天乳制品履约成本金额',
   delivery_dairy_cost_amt_180d DECIMAL(38,18) COMMENT '最近180天乳制品履约成本金额',
   delivery_dairy_cost_amt_365d DECIMAL(38,18) COMMENT '最近365天乳制品履约成本金额',
   delivery_dairy_cost_amt_his DECIMAL(38,18) COMMENT '历史所有乳制品履约成本金额',
   delivery_other_real_amt_7d DECIMAL(38,18) COMMENT '最近7天其他履约实付金额',
   delivery_other_real_amt_14d DECIMAL(38,18) COMMENT '最近14天其他履约实付金额',
   delivery_other_real_amt_30d DECIMAL(38,18) COMMENT '最近30天其他履约实付金额',
   delivery_other_real_amt_60d DECIMAL(38,18) COMMENT '最近60天其他履约实付金额',
   delivery_other_real_amt_180d DECIMAL(38,18) COMMENT '最近180天其他履约实付金额',
   delivery_other_real_amt_365d DECIMAL(38,18) COMMENT '最近365天其他履约实付金额',
   delivery_other_real_amt_his DECIMAL(38,18) COMMENT '历史所有其他履约实付金额',
   delivery_other_cost_amt_7d DECIMAL(38,18) COMMENT '最近7天其他履约成本金额',
   delivery_other_cost_amt_14d DECIMAL(38,18) COMMENT '最近14天其他履约成本金额',
   delivery_other_cost_amt_30d DECIMAL(38,18) COMMENT '最近30天其他履约成本金额',
   delivery_other_cost_amt_60d DECIMAL(38,18) COMMENT '最近60天其他履约成本金额',
   delivery_other_cost_amt_180d DECIMAL(38,18) COMMENT '最近180天其他履约成本金额',
   delivery_other_cost_amt_365d DECIMAL(38,18) COMMENT '最近365天其他履约成本金额',
   delivery_other_cost_amt_his DECIMAL(38,18) COMMENT '历史所有其他履约成本金额',
   delivery_self_real_amt_7d DECIMAL(38,18) COMMENT '最近7天自营品牌履约实付金额',
   delivery_self_real_amt_14d DECIMAL(38,18) COMMENT '最近14天自营品牌履约实付金额',
   delivery_self_real_amt_30d DECIMAL(38,18) COMMENT '最近30天自营品牌履约实付金额',
   delivery_self_real_amt_60d DECIMAL(38,18) COMMENT '最近60天自营品牌履约实付金额',
   delivery_self_real_amt_180d DECIMAL(38,18) COMMENT '最近180天自营品牌履约实付金额',
   delivery_self_real_amt_365d DECIMAL(38,18) COMMENT '最近365天自营品牌履约实付金额',
   delivery_self_real_amt_his DECIMAL(38,18) COMMENT '历史所有自营品牌履约实付金额',
   delivery_self_cost_amt_7d DECIMAL(38,18) COMMENT '最近7天自营品牌履约成本金额',
   delivery_self_cost_amt_14d DECIMAL(38,18) COMMENT '最近14天自营品牌履约成本金额',
   delivery_self_cost_amt_30d DECIMAL(38,18) COMMENT '最近30天自营品牌履约成本金额',
   delivery_self_cost_amt_60d DECIMAL(38,18) COMMENT '最近60天自营品牌履约成本金额',
   delivery_self_cost_amt_180d DECIMAL(38,18) COMMENT '最近180天自营品牌履约成本金额',
   delivery_self_cost_amt_365d DECIMAL(38,18) COMMENT '最近365天自营品牌履约成本金额',
   delivery_self_cost_amt_his DECIMAL(38,18) COMMENT '历史所有自营品牌履约成本金额',
   delivery_timing_real_amt_his DECIMAL(38,18) COMMENT '历史所有省心送履约实付金额',
   delivery_timing_cost_amt_his DECIMAL(38,18) COMMENT '历史所有省心送履约成本金额',
   first_relation_time DATETIME COMMENT '最早一次进入BD私海的时间',
   last_relation_time DATETIME COMMENT '最近一次进入BD私海的时间',
   relation_bd_cnt_30d BIGINT COMMENT '近30日分配bd人数',
   first_follow_time DATETIME COMMENT '首次拜访时间',
   last_follow_time DATETIME COMMENT '最近一次拜访时间',
   last_follow_bd_id BIGINT COMMENT '最近一次拜访bd Id',
   last_follow_up_way STRING COMMENT '最近一次跟进方式',
   last_follow_condition STRING COMMENT '最近一次跟进情况描述',
   last_follow_status BIGINT COMMENT '最近一次跟进状态，0未跟进，1已跟进，2已跟进且下单，3联系不上，4放弃跟进,9重置',
   follow_days_30d BIGINT COMMENT '近30日拜访天数',
   follow_bd_cnt_30d BIGINT COMMENT '近30日拜访bd人数',
   register_relation_duration BIGINT COMMENT '注册时间到首次分配BD时长（小时）',
   relation_follow_duration BIGINT COMMENT '首次分配BD到首次拜访时长（小时）',
   follow_order_duration BIGINT COMMENT '首次拜访到首单转化时长（小时）',
   register_audit_duration BIGINT COMMENT '注册时间到审核时长（小时）',
   audit_relation_duration BIGINT COMMENT '审核到首次分配BD时长（小时）',
   follow_efficient_cnt_7d BIGINT COMMENT '近7日有效或者上门拜访次数',
   follow_efficient_cnt_14d BIGINT COMMENT '近14日有效或者上门拜访次数',
   follow_efficient_cnt_30d BIGINT COMMENT '近30日有效或者上门拜访次数',
   first_coupon_issue_time DATETIME COMMENT '最早一次发放优惠券时间',
   last_coupon_issue_time DATETIME COMMENT '最近一次发放优惠券时间',
   first_coupon_order_time DATETIME COMMENT '最早一次使用优惠券时间',
   last_coupon_order_time DATETIME COMMENT '最近一次使用优惠券时间',
   last_coupon_name STRING COMMENT '最近一次使用优惠券名称',
   issue_coupon_cnt_his BIGINT COMMENT '历史发过优惠券张数',
   receive_coupon_cnt_his BIGINT COMMENT '历史领取过优惠券张数',
   order_coupon_cnt_his BIGINT COMMENT '历史下过单优惠券张数',
   order_coupon_cnt_list_his STRING COMMENT '历史下单过优惠券的优惠券名称张数',
   last_order_spuname STRING COMMENT '最近一次购买的商品名称',
   order_spuname_30d STRING COMMENT "最近30天购买的商品名称，例如：{'奥利奥中号饼干碎(无夹心)','安佳淡奶油','澄善金枕榴莲碎果肉','红凯特芒','越南红心火龙果'}",
   last_login_time DATETIME COMMENT '最近一次登录时间',
   last_sku_cl_time DATETIME COMMENT '最近一次点击商品详情页的时间',
   last_sku_cl_spuname STRING COMMENT '最近一次点击商品详情页的商品名称',
   last_sku_search_spuname STRING COMMENT '最近一次搜索的商品名称',
   last_sku_unorder_spuname STRING COMMENT '最近一次点击/搜索/加购的商品但是没有购买的商品名称',
   sku_cl_spuname_30d STRING COMMENT '最近30天点击商品详情页的商品名称',
   sku_cl_days_30d BIGINT COMMENT '最近30天点击商品详情页的天数',
   sku_cl_spu_cnt_30d BIGINT COMMENT '最近30天点击商品详情页的去重spu数',
   sku_cl_spuname_top5_30d STRING COMMENT '最近30天点击商品详情页排名前5的商品及点击数',
   sku_search_spuname_30d STRING COMMENT '最近30天搜索的商品名称',
   sku_unorder_spuname_30d STRING COMMENT '最近30天点击/搜索/加购的商品但是没有购买的商品名称',
   last_add_cart_time DATETIME COMMENT '最近一次加入购物车的时间',
   order_real_amt_1d DECIMAL(38,18) COMMENT '最近1天下单实付金额',
   order_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天下单应付金额',
   order_cnt_1d BIGINT COMMENT '最近1天订单数',
   order_sku_cnt_1d BIGINT COMMENT '最近1天订单商品件数',
   order_spu_cnt_1d BIGINT COMMENT '最近1天订单去重spu数',
   order_fruit_real_amt_1d DECIMAL(38,18) COMMENT '最近1天鲜果下单实付金额',
   order_fruit_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天鲜果下单应付金额',
   order_fruit_sku_cnt_1d BIGINT COMMENT '最近1天订单鲜果商品件数',
   order_dairy_real_amt_1d DECIMAL(38,18) COMMENT '最近1天乳制品下单实付金额',
   order_dairy_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天乳制品下单应付金额',
   order_dairy_sku_cnt_1d BIGINT COMMENT '最近1天订单乳制品商品件数',
   order_other_real_amt_1d DECIMAL(38,18) COMMENT '最近1天其他下单实付金额',
   order_other_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天其他下单应付金额',
   order_other_sku_cnt_1d BIGINT COMMENT '最近1天订单其他商品件数',
   order_self_real_amt_1d DECIMAL(38,18) COMMENT '最近1天自营品牌下单实付金额',
   order_self_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天自营品牌下单应付金额',
   order_self_sku_cnt_1d BIGINT COMMENT '最近1天订单自营品牌商品件数',
   order_timing_real_amt_1d DECIMAL(38,18) COMMENT '最近1天省心送下单实付金额',
   order_timing_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天省心送下单应付金额',
   order_timing_sku_cnt_1d BIGINT COMMENT '最近1天订单省心送商品件数',
   first_consign_order_time DATETIME COMMENT '最早一次代售(特指全品类商品，也就是sub_type=1或2的商品的订单，下同)下单时间',
   last_consign_order_time DATETIME COMMENT '最近一次代售下单时间',
   consign_order_real_amt_1d DECIMAL(38,18) COMMENT '最近1天代售下单实付金额',
   consign_order_real_amt_7d DECIMAL(38,18) COMMENT '最近7天代售下单实付金额',
   consign_order_real_amt_14d DECIMAL(38,18) COMMENT '最近14天代售下单实付金额',
   consign_order_real_amt_30d DECIMAL(38,18) COMMENT '最近30天代售下单实付金额',
   consign_order_real_amt_60d DECIMAL(38,18) COMMENT '最近60天代售下单实付金额',
   consign_order_real_amt_180d DECIMAL(38,18) COMMENT '最近180天代售下单实付金额',
   consign_order_real_amt_365d DECIMAL(38,18) COMMENT '最近365天代售下单实付金额',
   consign_order_real_amt_his DECIMAL(38,18) COMMENT '历史所有代售下单实付金额',
   consign_order_origin_amt_1d DECIMAL(38,18) COMMENT '最近1天代售下单应付金额',
   consign_order_origin_amt_7d DECIMAL(38,18) COMMENT '最近7天代售下单应付金额',
   consign_order_origin_amt_14d DECIMAL(38,18) COMMENT '最近14天代售下单应付金额',
   consign_order_origin_amt_30d DECIMAL(38,18) COMMENT '最近30天代售下单应付金额',
   consign_order_origin_amt_60d DECIMAL(38,18) COMMENT '最近60天代售下单应付金额',
   consign_order_origin_amt_180d DECIMAL(38,18) COMMENT '最近180天代售下单应付金额',
   consign_order_origin_amt_365d DECIMAL(38,18) COMMENT '最近365天代售下单应付金额',
   consign_order_origin_amt_his DECIMAL(38,18) COMMENT '历史所有代售下单应付金额',
   first_order_date_category4 STRING COMMENT '首单的四级类目列表, 例如：冷冻整果,测试类目C,草莓',
   loss_category4 STRING COMMENT '流失四级类目列表，指客户不再购买的四级类目列表，例如：奇异果丨猕猴桃,测试类目C,草莓',
   second_order_date_category4 STRING COMMENT '第二单的四级类目列表，例如：火龙果,芒果'
) 
PARTITIONED BY (ds STRING) 
STORED AS ALIORC  
TBLPROPERTIES ('comment'='客户标签表,含所有的客户的客户画像，也包含了客户所归属的BD情况等等');