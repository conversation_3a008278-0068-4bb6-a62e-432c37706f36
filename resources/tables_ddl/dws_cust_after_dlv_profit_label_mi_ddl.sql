CREATE TABLE summerfarm_ds.dws_cust_after_dlv_profit_label_mi (
  `cust_id` bigint COMMENT '客户ID',
  `dlv_profit_label` varchar(255) COMMENT '单客户履约后利润分层标签',
  `dlv_profit_rate_label` varchar(255) COMMENT '履约实付利润率标签：高（>=10%),低(<10%)',
  `life_cycle` varchar(255) COMMENT '单客户生命周期，枚举值: [准流失期,已流失期,成长期,新人期,沉默期,稳定期,适应期]',
  `life_cycle_detail` varchar(50) COMMENT '单客户生命周期细分标签, A1,A2,B1,B2等',
  `dlv_profit_group` varchar(10) COMMENT '履约后利润分层(大类：A，B，C)'
)
PARTITIONED BY (ds STRING COMMENT '数据日期') 
STORED AS ALIORC  
TBLPROPERTIES ('columnar.nested.type'='true',
	 'comment'='平台单店近30天履约后利润标签表, 请取最后一天的数据') 
LIFECYCLE 720;